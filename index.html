<!DOCTYPE html>
<html lang="hr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./calculator.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Kalkulacije.com | Besplatni Online Kalkulatori</title>
    <meta name="description" content="Besplatni online kalkulatori za svakodnevne potrebe. Izračunajte plaće, pretvarajte jedinice, računajte vrijeme, površinu i više." />
    <meta name="keywords" content="kalkulator, pretvarač jedinica, kalkulator plaće, bmi kalkulator, kalkulator datuma, kalkulator površine" />

    <!-- Preconnect for faster font loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Kalkulacije.com | Besplatni Online Kalkulatori" />
    <meta property="og:description" content="Besplatni online kalkulatori za svakodnevne potrebe. Izračunajte plaće, pretvarajte jedinice, računajte vrijeme, površinu i više." />
    <meta property="og:image" content="https://kalkulacije.com/og-image.svg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:url" content="https://kalkulacije.com/" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Kalkulacije.com | Besplatni Online Kalkulatori" />
    <meta name="twitter:description" content="Besplatni online kalkulatori za svakodnevne potrebe. Izračunajte plaće, pretvarajte jedinice, računajte vrijeme, površinu i više." />
    <meta name="twitter:image" content="https://kalkulacije.com/og-image.svg" />

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-KMWE9DLRD1"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-KMWE9DLRD1');
    </script>

    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "owjgqfafcf");
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="./src/main.tsx"></script>
  </body>
</html>